/**
 * Modal styles for Historify
 */

/* Modal container */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.modal.hidden {
  display: none;
}

/* Modal backdrop */
.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-backdrop.show {
  opacity: 1;
}

/* Modal content */
.modal-content {
  position: relative;
  width: 100%;
  max-width: 600px;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  margin: 1.5rem;
  opacity: 0;
  transform: scale(0.95);
  transition: all 0.3s ease;
  z-index: 10;
  max-height: 90vh;
  overflow: hidden;
}

.modal-content.show {
  opacity: 1;
  transform: scale(1);
}

.modal-content.max-w-4xl {
  max-width: 56rem;
}

/* Dark mode styles */
[data-theme="dark"] .modal-content {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* Modal parts */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-primary);
}

.modal-body {
  padding: 1.25rem;
  max-height: calc(100vh - 15rem);
  overflow-y: auto;
}

.modal-footer {
  padding: 1.25rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  border-top: 1px solid var(--border-primary);
}

/* Responsive adjustments */
@media (min-width: 640px) {
  .modal-content {
    margin: 0 2rem;
  }
}

@media (max-width: 639px) {
  .modal-content {
    margin: 0 1rem;
    max-height: 90vh;
  }
  
  .modal-body {
    max-height: calc(90vh - 10rem);
  }
}
