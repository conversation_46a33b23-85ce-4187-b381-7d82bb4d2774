/**
 * Historify Design System
 * Modern, professional-grade design tokens and components
 */

/* ===== CSS Variables & Design Tokens ===== */
:root {
  /* Color Palette - Light Mode */
  --primary: #6366F1;
  --primary-hover: #5558E9;
  --primary-light: #E9EAFC;
  --secondary: #8B5CF6;
  --accent: #3B82F6;
  
  /* Semantic Colors */
  --success: #10B981;
  --success-light: #D1FAE5;
  --warning: #F59E0B;
  --warning-light: #FEF3C7;
  --error: #EF4444;
  --error-light: #FEE2E2;
  --info: #3B82F6;
  --info-light: #DBEAFE;
  
  /* Grays */
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
  
  /* Background Colors */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F9FAFB;
  --bg-tertiary: #F3F4F6;
  --bg-card: #FFFFFF;
  --bg-hover: rgba(99, 102, 241, 0.04);
  
  /* Text Colors */
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --text-tertiary: #9CA3AF;
  --text-inverse: #FFFFFF;
  
  /* Border Colors */
  --border-primary: #E5E7EB;
  --border-secondary: #D1D5DB;
  --border-focus: #6366F1;
  
  /* Z-index Scale */
  --z-dropdown: 10;
  --z-sticky: 20;
  --z-banner: 30;
  --z-overlay: 40;
  --z-modal: 50;
  --z-popover: 60;
  --z-tooltip: 70;
  --z-toast: 80;
  
  /* Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Spacing (8px grid) */
  --space-1: 0.5rem;
  --space-2: 1rem;
  --space-3: 1.5rem;
  --space-4: 2rem;
  --space-5: 2.5rem;
  --space-6: 3rem;
  --space-8: 4rem;
  --space-10: 5rem;
  --space-12: 6rem;
  --space-16: 8rem;
  
  /* Typography */
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  
  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
  
  /* Font Weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-base: 200ms ease;
  --transition-slow: 300ms ease;
  
  /* Z-Index Scale */
  --z-base: 0;
  --z-dropdown: 1000;
  --z-sticky: 1100;
  --z-fixed: 1200;
  --z-modal-backdrop: 1300;
  --z-modal: 1400;
  --z-popover: 1500;
  --z-tooltip: 1600;
}

/* Dark Mode Variables */
[data-theme="dark"] {
  /* Backgrounds */
  --bg-primary: #0F172A;
  --bg-secondary: #1E293B;
  --bg-tertiary: #293548;
  --bg-card: #1E293B;
  --bg-hover: rgba(99, 102, 241, 0.1);
  
  /* Text */
  --text-primary: #F1F5F9;
  --text-secondary: #CBD5E1;
  --text-tertiary: #94A3B8;
  
  /* Borders */
  --border-primary: #334155;
  --border-secondary: #475569;
  
  /* Adjust colors for dark mode */
  --primary-light: #312E81;
  --success-light: #065F46;
  --warning-light: #78350F;
  --error-light: #7F1D1D;
  --info-light: #1E3A8A;
}

/* ===== Base Styles ===== */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-sans);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  line-height: var(--leading-normal);
  transition: background-color var(--transition-slow), color var(--transition-slow);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  margin: 0;
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

.text-muted {
  color: var(--text-secondary);
}

.text-mono {
  font-family: var(--font-mono);
}

/* ===== Layout Components ===== */
.container-fluid {
  width: 100%;
  padding: 0 var(--space-4);
  margin: 0 auto;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* Card Component */
.card-modern {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.card-modern:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

.card-body {
  padding: var(--space-4);
}

/* ===== Modern Button Styles ===== */
.btn-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
  padding: 0.625rem 1.25rem;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  border-radius: var(--radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn-modern:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.btn-modern:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
  background: var(--primary);
  color: var(--text-inverse);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--border-secondary);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* Button Sizes */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: var(--text-xs);
}

.btn-lg {
  padding: 0.875rem 1.75rem;
  font-size: var(--text-base);
}

/* ===== Modern Input Styles ===== */
.input-modern {
  width: 100%;
  padding: 0.625rem 1rem;
  font-size: var(--text-sm);
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  color: var(--text-primary);
}

.input-modern:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.input-modern::placeholder {
  color: var(--text-tertiary);
}

/* Input Group */
.input-group {
  position: relative;
  width: 100%;
}

.input-label {
  position: absolute;
  top: -0.625rem;
  left: 0.875rem;
  padding: 0 0.25rem;
  background: var(--bg-primary);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

/* ===== Data Table Styles ===== */
.table-modern {
  width: 100%;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
}

.table-modern thead {
  background: var(--bg-secondary);
}

.table-modern th {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--text-secondary);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.table-modern td {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  border-bottom: 1px solid var(--border-primary);
}

.table-modern tbody tr {
  transition: all var(--transition-fast);
}

.table-modern tbody tr:hover {
  background: var(--bg-hover);
}

.table-modern tbody tr:last-child td {
  border-bottom: none;
}

/* ===== Modal Styles ===== */
.modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  transition: opacity var(--transition-slow);
}

.modal-backdrop.show {
  opacity: 1;
}

.modal-container {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--space-4);
}

.modal-content {
  background: var(--bg-card);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.95);
  opacity: 0;
  transition: all var(--transition-slow);
}

.modal-content.show {
  transform: scale(1);
  opacity: 1;
}

/* ===== Progress Components ===== */
.progress-ring {
  position: relative;
  display: inline-block;
}

.progress-ring svg {
  transform: rotate(-90deg);
}

.progress-ring-bg {
  stroke: var(--gray-200);
}

.progress-ring-bar {
  stroke: var(--primary);
  stroke-linecap: round;
  transition: stroke-dashoffset var(--transition-slow);
}

.progress-linear {
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-linear-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
}

/* ===== Badge Styles ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.badge-success {
  background: var(--success-light);
  color: var(--success);
}

.badge-warning {
  background: var(--warning-light);
  color: var(--warning);
}

.badge-error {
  background: var(--error-light);
  color: var(--error);
}

.badge-info {
  background: var(--info-light);
  color: var(--info);
}

/* ===== Sidebar Navigation ===== */
.sidebar {
  width: 280px;
  height: 100vh;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  transition: all var(--transition-slow);
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-nav {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-2);
}

.nav-item {
  margin-bottom: var(--space-1);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-3);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.nav-link:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.nav-link.active {
  background: var(--primary-light);
  color: var(--primary);
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* ===== Toast Notifications ===== */
.toast {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--space-2);
  min-width: 300px;
  animation: slideIn var(--transition-slow) ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.toast-success {
  border-color: var(--success);
}

.toast-error {
  border-color: var(--error);
}

/* ===== Loading States ===== */
.skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-300) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* ===== Utility Classes ===== */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }

.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }

.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }

/* ===== Animations ===== */
.fade-in {
  animation: fadeIn var(--transition-slow) ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp var(--transition-slow) ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Status Badges */
.bg-success\/10 {
  background-color: rgba(34, 197, 94, 0.1);
}

.bg-warning\/10 {
  background-color: rgba(251, 191, 36, 0.1);
}

.bg-error\/10 {
  background-color: rgba(239, 68, 68, 0.1);
}

.bg-info\/10 {
  background-color: rgba(59, 130, 246, 0.1);
}