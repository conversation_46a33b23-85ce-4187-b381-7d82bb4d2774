<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient definitions -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#93c5fd;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:0.8" />
    </linearGradient>
    
    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#1e40af" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="90" fill="url(#bgGradient)" filter="url(#shadow)"/>
  
  <!-- Inner circle for depth -->
  <circle cx="100" cy="100" r="75" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
  
  <!-- Stylized "H" -->
  <!-- Left vertical bar -->
  <rect x="50" y="60" width="12" height="80" rx="6" fill="white" opacity="0.95"/>
  
  <!-- Right vertical bar -->
  <rect x="138" y="60" width="12" height="80" rx="6" fill="white" opacity="0.95"/>
  
  <!-- Horizontal connecting bar with chart pattern -->
  <rect x="62" y="95" width="76" height="10" rx="5" fill="white" opacity="0.95"/>
  
  <!-- Chart elements integrated into the design -->
  <!-- Candlestick bars -->
  <g opacity="0.9">
    <!-- Candlestick 1 -->
    <rect x="75" y="85" width="4" height="20" fill="#fbbf24"/>
    <line x1="77" y1="80" x2="77" y2="85" stroke="#fbbf24" stroke-width="2" stroke-linecap="round"/>
    <line x1="77" y1="105" x2="77" y2="110" stroke="#fbbf24" stroke-width="2" stroke-linecap="round"/>
    
    <!-- Candlestick 2 -->
    <rect x="90" y="75" width="4" height="30" fill="#10b981"/>
    <line x1="92" y1="70" x2="92" y2="75" stroke="#10b981" stroke-width="2" stroke-linecap="round"/>
    <line x1="92" y1="105" x2="92" y2="115" stroke="#10b981" stroke-width="2" stroke-linecap="round"/>
    
    <!-- Candlestick 3 -->
    <rect x="120" y="80" width="4" height="25" fill="#f59e0b"/>
    <line x1="122" y1="75" x2="122" y2="80" stroke="#f59e0b" stroke-width="2" stroke-linecap="round"/>
    <line x1="122" y1="105" x2="122" y2="112" stroke="#f59e0b" stroke-width="2" stroke-linecap="round"/>
  </g>
  
  <!-- Trending line -->
  <g opacity="0.8">
    <path d="M 30 130 Q 70 125 100 120 T 170 110" 
          stroke="white" stroke-width="3" fill="none" stroke-linecap="round"/>
    <!-- Arrow head -->
    <polygon points="165,107 175,110 165,113" fill="white"/>
  </g>
  
  <!-- Data points -->
  <g opacity="0.7">
    <circle cx="40" cy="70" r="3" fill="white"/>
    <circle cx="160" cy="130" r="3" fill="white"/>
    <circle cx="70" cy="140" r="2.5" fill="rgba(255,255,255,0.8)"/>
    <circle cx="130" cy="65" r="2.5" fill="rgba(255,255,255,0.8)"/>
  </g>
  
  <!-- Subtle shine effect -->
  <ellipse cx="80" cy="75" rx="25" ry="15" fill="rgba(255,255,255,0.2)" transform="rotate(-30 80 75)"/>
  
</svg>