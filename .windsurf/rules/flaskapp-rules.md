---
trigger: always_on
---

1)Write a Modular Flask Application using Jinja Template
2)Use Tailwind CSS CDN and DaisyUI CDN as well for building frontend
3)Build a cool moden UI Templates
4)Database : Sqlite and Flask SqlAlchemy and Alchemy
5)Routes use modular Blueprints
6)Run the port at 5001
7)Any sensitive details keep in .env files
8)write a documentation (readme.md)
9)Licensing - MIT License (create License file) 
10)also include .gitignore
11)Write an CSS files/JS only in static (CSS/JS) folder. Avoid writing inline CSS and JS
12)Write the base.html with headers, footers, menu
13)Toggle between light and dark themes