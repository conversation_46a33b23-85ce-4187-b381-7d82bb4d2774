# Virtual Environment
venv/
env/
.env/
.venv/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Flask
instance/
.webassets-cache

# Database
*.sqlite
*.sqlite3
*.db

# IDE - VS Code
.vscode/
.history

# IDE - PyCharm
.idea/
*.iml
*.iws
*.ipr

# IDE - Jupyter Notebook
.ipynb_checkpoints

# OS specific
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production